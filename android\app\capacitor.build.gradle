// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-camera')
    implementation project(':capacitor-network')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-toast')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
