/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { OrderItem } from './OrderItem';
import type { UserAiAvatarVO } from './UserAiAvatarVO';
export type Page_UserAiAvatarVO_ = {
  countId?: string;
  current?: number;
  maxLimit?: number;
  optimizeCountSql?: boolean;
  orders?: Array<OrderItem>;
  pages?: number;
  records?: Array<UserAiAvatarVO>;
  searchCount?: boolean;
  size?: number;
  total?: number;
};
