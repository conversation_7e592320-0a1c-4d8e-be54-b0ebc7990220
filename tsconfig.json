{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "lib": ["DOM", "ES2020"],
    "jsx": "preserve",
    "target": "ES2020",
    "noEmit": true,
    "skipLibCheck": true,
    "jsxImportSource": "vue",
    "useDefineForClassFields": true,

    /* modules */
    "module": "ESNext",
    "isolatedModules": true,
    "resolveJsonModule": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,

    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noUncheckedIndexedAccess": true
  },
  "include": ["src"]
}
