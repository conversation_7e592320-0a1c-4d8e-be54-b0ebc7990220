/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type UserLearningRecordVO = {
  accuracy?: number;
  count?: number;
  createTime?: string;
  duration?: number;
  experience?: number;
  formattedDuration?: string;
  id?: number;
  lessonNumber?: number;
  points?: number;
  recordDate?: string;
  recordType?: string;
  recordTypeName?: string;
  relatedId?: number;
  relatedName?: string;
  remark?: string;
  status?: string;
  statusName?: string;
  userAvatar?: string;
  userId?: number;
  userName?: string;
};
