<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8fbff" />
      <stop offset="100%" stop-color="#e6f0ff" />
    </linearGradient>
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#a0d8ff" />
      <stop offset="100%" stop-color="#7ac7ff" />
    </linearGradient>
    <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#5a9ee2" />
      <stop offset="100%" stop-color="#3a80d2" />
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>

  <!-- 背景科技元素 - 更精细的电路板纹理 -->
  <g opacity="0.15">
    <path d="M15,20 L45,20 L45,35 L60,35 L60,55 L35,55 L35,75 L55,75 L55,95 L75,95 L75,75 L95,75 L95,50 L75,50 L75,30 L95,30 L95,10"
          fill="none" stroke="url(#techGradient)" stroke-width="1.2" />
    <path d="M115,10 L115,30 L135,30 L135,50 L160,50 L160,70 L175,70 L175,95 L195,95"
          fill="none" stroke="url(#techGradient)" stroke-width="1.2" />
    <circle cx="45" cy="35" r="2" fill="#4a90e2" />
    <circle cx="75" cy="50" r="2" fill="#4a90e2" />
    <circle cx="135" cy="50" r="2" fill="#4a90e2" />
    <circle cx="175" cy="70" r="2" fill="#4a90e2" />
  </g>

  <!-- 改进的星星元素 - 更精致的设计 -->
  <g filter="url(#glow)">
    <path d="M40,40 L42.5,48 L36,44 L44,44 L37.5,48 Z" fill="url(#starGradient)"/>
    <path d="M170,45 L173,53 L166.5,49 L179.5,49 L173,53 Z" fill="url(#starGradient)"/>
    <path d="M180,110 L182.5,118 L176,114 L184,114 L177.5,118 Z" fill="url(#starGradient)"/>
    <path d="M25,130 L27.5,138 L21,134 L29,134 L22.5,138 Z" fill="url(#starGradient)"/>
    <path d="M100,20 L103,28 L96.5,24 L109.5,24 L103,28 Z" fill="url(#starGradient)"/>
    <path d="M155,155 L157.5,163 L151,159 L159,159 L152.5,163 Z" fill="url(#starGradient)"/>
    <!-- 添加几个小星星点缀 -->
    <circle cx="60" cy="60" r="1.5" fill="#a0d8ff" />
    <circle cx="160" cy="130" r="1.5" fill="#a0d8ff" />
    <circle cx="40" cy="150" r="1.5" fill="#a0d8ff" />
    <circle cx="190" cy="60" r="1.5" fill="#a0d8ff" />
  </g>

  <!-- 优化的云形状 - 更流畅的曲线 -->
  <path d="M60,120 C40,120 30,105 30,90 C30,75 40,60 60,60 C60,40 80,25 100,25 C120,25 140,40 140,60 C160,60 170,75 170,90 C170,105 160,120 140,120 Z"
        fill="url(#cloudGradient)" stroke="#4a90e2" stroke-width="2.5" filter="url(#glow)"/>

  <!-- 更精细的数据流动效果 -->
  <g opacity="0.7">
    <path d="M40,90 L60,90 L70,80 L90,80 L100,90 L120,90 L130,80 L150,80"
          fill="none" stroke="#5a9ee2" stroke-width="1.5" stroke-dasharray="3,2"/>
    <path d="M50,100 L70,100 L80,110 L100,110 L110,100 L130,100 L140,110 L160,110"
          fill="none" stroke="#5a9ee2" stroke-width="1.5" stroke-dasharray="3,2"/>
    <path d="M90,40 C110,50 120,70 100,90"
          fill="none" stroke="#5a9ee2" stroke-width="1" stroke-dasharray="2,2"/>
    <path d="M70,40 C50,60 60,80 80,90"
          fill="none" stroke="#5a9ee2" stroke-width="1" stroke-dasharray="2,2"/>
  </g>

  <!-- 改进的中心科技图案 -->
  <g transform="translate(100,70)">
    <circle r="18" fill="none" stroke="url(#techGradient)" stroke-width="1.5" stroke-dasharray="4,2"/>
    <circle r="12" fill="none" stroke="url(#techGradient)" stroke-width="1.5" opacity="0.7"/>
    <circle r="6" fill="url(#techGradient)" opacity="0.9"/>

    <!-- 优化的连接线 -->
    <line x1="-18" y1="0" x2="-25" y2="0" stroke="url(#techGradient)" stroke-width="1.5"/>
    <line x1="18" y1="0" x2="25" y2="0" stroke="url(#techGradient)" stroke-width="1.5"/>
    <line x1="0" y1="-18" x2="0" y2="-25" stroke="url(#techGradient)" stroke-width="1.5"/>
    <line x1="0" y1="18" x2="0" y2="25" stroke="url(#techGradient)" stroke-width="1.5"/>

    <line x1="-12" y1="-12" x2="-17" y2="-17" stroke="url(#techGradient)" stroke-width="1" opacity="0.7"/>
    <line x1="12" y1="-12" x2="17" y2="-17" stroke="url(#techGradient)" stroke-width="1" opacity="0.7"/>
    <line x1="-12" y1="12" x2="-17" y2="17" stroke="url(#techGradient)" stroke-width="1" opacity="0.7"/>
    <line x1="12" y1="12" x2="17" y2="17" stroke="url(#techGradient)" stroke-width="1" opacity="0.7"/>
  </g>

  <!-- 智能元素 - 更简洁的设计 -->
  <circle cx="85" cy="65" r="2.5" fill="#4a90e2"/>
  <circle cx="115" cy="65" r="2.5" fill="#4a90e2"/>


  <!-- 添加额外的连接点 -->
  <circle cx="70" cy="60" r="1.5" fill="#4a90e2"/>
  <circle cx="130" cy="60" r="1.5" fill="#4a90e2"/>
  <circle cx="100" cy="105" r="1.5" fill="#4a90e2"/>
  <circle cx="65" cy="95" r="1.5" fill="#4a90e2"/>
  <circle cx="135" cy="95" r="1.5" fill="#4a90e2"/>
</svg>
