/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export { ApiError } from './core/ApiError';
export { CancelablePromise, CancelError } from './core/CancelablePromise';
export type { OpenAPIConfig } from './core/OpenAPI';
export { OpenAPI } from './core/OpenAPI';

export type { AiAvatarAddRequest } from './models/AiAvatarAddRequest';
export type { AiAvatarUpdateRequest } from './models/AiAvatarUpdateRequest';
export type { AiAvatarVO } from './models/AiAvatarVO';
export type { Announcement } from './models/Announcement';
export type { AnnouncementAddRequest } from './models/AnnouncementAddRequest';
export type { AnnouncementUpdateRequest } from './models/AnnouncementUpdateRequest';
export type { AnnouncementVO } from './models/AnnouncementVO';
export type { BaseResponse_AiAvatarVO_ } from './models/BaseResponse_AiAvatarVO_';
export type { BaseResponse_AnnouncementVO_ } from './models/BaseResponse_AnnouncementVO_';
export type { BaseResponse_Array_int_ } from './models/BaseResponse_Array_int_';
export type { BaseResponse_boolean_ } from './models/BaseResponse_boolean_';
export type { BaseResponse_ChatMessageVO_ } from './models/BaseResponse_ChatMessageVO_';
export type { BaseResponse_Course_ } from './models/BaseResponse_Course_';
export type { BaseResponse_CourseCategory_ } from './models/BaseResponse_CourseCategory_';
export type { BaseResponse_CourseChapter_ } from './models/BaseResponse_CourseChapter_';
export type { BaseResponse_CourseMaterial_ } from './models/BaseResponse_CourseMaterial_';
export type { BaseResponse_CourseReviewVO_ } from './models/BaseResponse_CourseReviewVO_';
export type { BaseResponse_CourseSection_ } from './models/BaseResponse_CourseSection_';
export type { BaseResponse_CourseVO_ } from './models/BaseResponse_CourseVO_';
export type { BaseResponse_DailyArticleVO_ } from './models/BaseResponse_DailyArticleVO_';
export type { BaseResponse_DailyWordVO_ } from './models/BaseResponse_DailyWordVO_';
export type { BaseResponse_FriendRequestVO_ } from './models/BaseResponse_FriendRequestVO_';
export type { BaseResponse_int_ } from './models/BaseResponse_int_';
export type { BaseResponse_List_AiAvatarVO_ } from './models/BaseResponse_List_AiAvatarVO_';
export type { BaseResponse_List_ChatMessageVO_ } from './models/BaseResponse_List_ChatMessageVO_';
export type { BaseResponse_List_ChatSessionVO_ } from './models/BaseResponse_List_ChatSessionVO_';
export type { BaseResponse_List_Course_ } from './models/BaseResponse_List_Course_';
export type { BaseResponse_List_CourseCategory_ } from './models/BaseResponse_List_CourseCategory_';
export type { BaseResponse_List_CourseChapter_ } from './models/BaseResponse_List_CourseChapter_';
export type { BaseResponse_List_CourseMaterial_ } from './models/BaseResponse_List_CourseMaterial_';
export type { BaseResponse_List_CourseSection_ } from './models/BaseResponse_List_CourseSection_';
export type { BaseResponse_List_CourseVO_ } from './models/BaseResponse_List_CourseVO_';
export type { BaseResponse_List_FriendRelationshipVO_ } from './models/BaseResponse_List_FriendRelationshipVO_';
export type { BaseResponse_List_FriendRequestVO_ } from './models/BaseResponse_List_FriendRequestVO_';
export type { BaseResponse_List_long_ } from './models/BaseResponse_List_long_';
export type { BaseResponse_List_Map_string_object_ } from './models/BaseResponse_List_Map_string_object_';
export type { BaseResponse_List_PrivateChatSessionVO_ } from './models/BaseResponse_List_PrivateChatSessionVO_';
export type { BaseResponse_List_TeacherVO_ } from './models/BaseResponse_List_TeacherVO_';
export type { BaseResponse_List_UserAiAvatarVO_ } from './models/BaseResponse_List_UserAiAvatarVO_';
export type { BaseResponse_List_UserFeedbackReplyVO_ } from './models/BaseResponse_List_UserFeedbackReplyVO_';
export type { BaseResponse_List_UserLevelVO_ } from './models/BaseResponse_List_UserLevelVO_';
export type { BaseResponse_List_UserWordBookVO_ } from './models/BaseResponse_List_UserWordBookVO_';
export type { BaseResponse_LoginUserVO_ } from './models/BaseResponse_LoginUserVO_';
export type { BaseResponse_long_ } from './models/BaseResponse_long_';
export type { BaseResponse_Map_string_object_ } from './models/BaseResponse_Map_string_object_';
export type { BaseResponse_Map_string_string_ } from './models/BaseResponse_Map_string_string_';
export type { BaseResponse_object_ } from './models/BaseResponse_object_';
export type { BaseResponse_Page_AiAvatarVO_ } from './models/BaseResponse_Page_AiAvatarVO_';
export type { BaseResponse_Page_Announcement_ } from './models/BaseResponse_Page_Announcement_';
export type { BaseResponse_Page_AnnouncementVO_ } from './models/BaseResponse_Page_AnnouncementVO_';
export type { BaseResponse_Page_ChatMessageVO_ } from './models/BaseResponse_Page_ChatMessageVO_';
export type { BaseResponse_Page_CourseChapter_ } from './models/BaseResponse_Page_CourseChapter_';
export type { BaseResponse_Page_CourseMaterial_ } from './models/BaseResponse_Page_CourseMaterial_';
export type { BaseResponse_Page_CourseReviewVO_ } from './models/BaseResponse_Page_CourseReviewVO_';
export type { BaseResponse_Page_CourseSection_ } from './models/BaseResponse_Page_CourseSection_';
export type { BaseResponse_Page_CourseVO_ } from './models/BaseResponse_Page_CourseVO_';
export type { BaseResponse_Page_DailyArticle_ } from './models/BaseResponse_Page_DailyArticle_';
export type { BaseResponse_Page_DailyArticleVO_ } from './models/BaseResponse_Page_DailyArticleVO_';
export type { BaseResponse_Page_DailyWord_ } from './models/BaseResponse_Page_DailyWord_';
export type { BaseResponse_Page_DailyWordVO_ } from './models/BaseResponse_Page_DailyWordVO_';
export type { BaseResponse_Page_FriendRelationship_ } from './models/BaseResponse_Page_FriendRelationship_';
export type { BaseResponse_Page_FriendRelationshipVO_ } from './models/BaseResponse_Page_FriendRelationshipVO_';
export type { BaseResponse_Page_FriendRequest_ } from './models/BaseResponse_Page_FriendRequest_';
export type { BaseResponse_Page_FriendRequestVO_ } from './models/BaseResponse_Page_FriendRequestVO_';
export type { BaseResponse_Page_Post_ } from './models/BaseResponse_Page_Post_';
export type { BaseResponse_Page_PostCommentReplyVO_ } from './models/BaseResponse_Page_PostCommentReplyVO_';
export type { BaseResponse_Page_PostCommentVO_ } from './models/BaseResponse_Page_PostCommentVO_';
export type { BaseResponse_Page_PostVO_ } from './models/BaseResponse_Page_PostVO_';
export type { BaseResponse_Page_PrivateMessageVO_ } from './models/BaseResponse_Page_PrivateMessageVO_';
export type { BaseResponse_Page_Teacher_ } from './models/BaseResponse_Page_Teacher_';
export type { BaseResponse_Page_TeacherVO_ } from './models/BaseResponse_Page_TeacherVO_';
export type { BaseResponse_Page_User_ } from './models/BaseResponse_Page_User_';
export type { BaseResponse_Page_UserAiAvatarVO_ } from './models/BaseResponse_Page_UserAiAvatarVO_';
export type { BaseResponse_Page_UserFeedback_ } from './models/BaseResponse_Page_UserFeedback_';
export type { BaseResponse_Page_UserFeedbackReplyVO_ } from './models/BaseResponse_Page_UserFeedbackReplyVO_';
export type { BaseResponse_Page_UserLearningRecordVO_ } from './models/BaseResponse_Page_UserLearningRecordVO_';
export type { BaseResponse_Page_UserLevel_ } from './models/BaseResponse_Page_UserLevel_';
export type { BaseResponse_Page_UserLevelVO_ } from './models/BaseResponse_Page_UserLevelVO_';
export type { BaseResponse_Page_UserVO_ } from './models/BaseResponse_Page_UserVO_';
export type { BaseResponse_Page_UserWordBookVO_ } from './models/BaseResponse_Page_UserWordBookVO_';
export type { BaseResponse_PostCommentReplyVO_ } from './models/BaseResponse_PostCommentReplyVO_';
export type { BaseResponse_PostCommentVO_ } from './models/BaseResponse_PostCommentVO_';
export type { BaseResponse_PostVO_ } from './models/BaseResponse_PostVO_';
export type { BaseResponse_PrivateChatSessionVO_ } from './models/BaseResponse_PrivateChatSessionVO_';
export type { BaseResponse_string_ } from './models/BaseResponse_string_';
export type { BaseResponse_Teacher_ } from './models/BaseResponse_Teacher_';
export type { BaseResponse_TeacherVO_ } from './models/BaseResponse_TeacherVO_';
export type { BaseResponse_User_ } from './models/BaseResponse_User_';
export type { BaseResponse_UserAiAvatarVO_ } from './models/BaseResponse_UserAiAvatarVO_';
export type { BaseResponse_UserDailyWord_ } from './models/BaseResponse_UserDailyWord_';
export type { BaseResponse_UserFeedback_ } from './models/BaseResponse_UserFeedback_';
export type { BaseResponse_UserLearningRecordVO_ } from './models/BaseResponse_UserLearningRecordVO_';
export type { BaseResponse_UserLearningStats_ } from './models/BaseResponse_UserLearningStats_';
export type { BaseResponse_UserLevel_ } from './models/BaseResponse_UserLevel_';
export type { BaseResponse_UserLevelVO_ } from './models/BaseResponse_UserLevelVO_';
export type { BaseResponse_UserVO_ } from './models/BaseResponse_UserVO_';
export type { ChatMessageAddRequest } from './models/ChatMessageAddRequest';
export type { ChatMessageVO } from './models/ChatMessageVO';
export type { ChatSessionUpdateRequest } from './models/ChatSessionUpdateRequest';
export type { ChatSessionVO } from './models/ChatSessionVO';
export type { Course } from './models/Course';
export type { CourseAddRequest } from './models/CourseAddRequest';
export type { CourseCategory } from './models/CourseCategory';
export type { CourseChapter } from './models/CourseChapter';
export type { CourseFavourAddRequest } from './models/CourseFavourAddRequest';
export type { CourseMaterial } from './models/CourseMaterial';
export type { CourseReview } from './models/CourseReview';
export type { CourseReviewVO } from './models/CourseReviewVO';
export type { CourseSection } from './models/CourseSection';
export type { CourseUpdateRequest } from './models/CourseUpdateRequest';
export type { CourseVO } from './models/CourseVO';
export type { DailyArticle } from './models/DailyArticle';
export type { DailyArticleAddRequest } from './models/DailyArticleAddRequest';
export type { DailyArticleQueryRequest } from './models/DailyArticleQueryRequest';
export type { DailyArticleUpdateRequest } from './models/DailyArticleUpdateRequest';
export type { DailyArticleVO } from './models/DailyArticleVO';
export type { DailyWord } from './models/DailyWord';
export type { DailyWordAddRequest } from './models/DailyWordAddRequest';
export type { DailyWordUpdateRequest } from './models/DailyWordUpdateRequest';
export type { DailyWordVO } from './models/DailyWordVO';
export type { DeleteRequest } from './models/DeleteRequest';
export type { DeleteRequest_1 } from './models/DeleteRequest_1';
export type { FriendRelationship } from './models/FriendRelationship';
export type { FriendRelationshipAddRequest } from './models/FriendRelationshipAddRequest';
export type { FriendRelationshipUpdateRequest } from './models/FriendRelationshipUpdateRequest';
export type { FriendRelationshipVO } from './models/FriendRelationshipVO';
export type { FriendRequest } from './models/FriendRequest';
export type { FriendRequestAddRequest } from './models/FriendRequestAddRequest';
export type { FriendRequestQueryRequest } from './models/FriendRequestQueryRequest';
export type { FriendRequestVO } from './models/FriendRequestVO';
export type { LoginUserVO } from './models/LoginUserVO';
export type { Map_string_object_ } from './models/Map_string_object_';
export type { Map_string_object__1 } from './models/Map_string_object__1';
export type { Map_string_object__2 } from './models/Map_string_object__2';
export type { Map_string_object__3 } from './models/Map_string_object__3';
export type { Map_string_string_ } from './models/Map_string_string_';
export type { OrderItem } from './models/OrderItem';
export type { Page_AiAvatarVO_ } from './models/Page_AiAvatarVO_';
export type { Page_Announcement_ } from './models/Page_Announcement_';
export type { Page_AnnouncementVO_ } from './models/Page_AnnouncementVO_';
export type { Page_ChatMessageVO_ } from './models/Page_ChatMessageVO_';
export type { Page_CourseChapter_ } from './models/Page_CourseChapter_';
export type { Page_CourseMaterial_ } from './models/Page_CourseMaterial_';
export type { Page_CourseReviewVO_ } from './models/Page_CourseReviewVO_';
export type { Page_CourseSection_ } from './models/Page_CourseSection_';
export type { Page_CourseVO_ } from './models/Page_CourseVO_';
export type { Page_DailyArticle_ } from './models/Page_DailyArticle_';
export type { Page_DailyArticleVO_ } from './models/Page_DailyArticleVO_';
export type { Page_DailyWord_ } from './models/Page_DailyWord_';
export type { Page_DailyWordVO_ } from './models/Page_DailyWordVO_';
export type { Page_FriendRelationship_ } from './models/Page_FriendRelationship_';
export type { Page_FriendRelationshipVO_ } from './models/Page_FriendRelationshipVO_';
export type { Page_FriendRequest_ } from './models/Page_FriendRequest_';
export type { Page_FriendRequestVO_ } from './models/Page_FriendRequestVO_';
export type { Page_Post_ } from './models/Page_Post_';
export type { Page_PostCommentReplyVO_ } from './models/Page_PostCommentReplyVO_';
export type { Page_PostCommentVO_ } from './models/Page_PostCommentVO_';
export type { Page_PostVO_ } from './models/Page_PostVO_';
export type { Page_PrivateMessageVO_ } from './models/Page_PrivateMessageVO_';
export type { Page_Teacher_ } from './models/Page_Teacher_';
export type { Page_TeacherVO_ } from './models/Page_TeacherVO_';
export type { Page_User_ } from './models/Page_User_';
export type { Page_UserAiAvatarVO_ } from './models/Page_UserAiAvatarVO_';
export type { Page_UserFeedback_ } from './models/Page_UserFeedback_';
export type { Page_UserFeedbackReplyVO_ } from './models/Page_UserFeedbackReplyVO_';
export type { Page_UserLearningRecordVO_ } from './models/Page_UserLearningRecordVO_';
export type { Page_UserLevel_ } from './models/Page_UserLevel_';
export type { Page_UserLevelVO_ } from './models/Page_UserLevelVO_';
export type { Page_UserVO_ } from './models/Page_UserVO_';
export type { Page_UserWordBookVO_ } from './models/Page_UserWordBookVO_';
export type { Post } from './models/Post';
export type { PostAddRequest } from './models/PostAddRequest';
export type { PostCommentAddRequest } from './models/PostCommentAddRequest';
export type { PostCommentReplyAddRequest } from './models/PostCommentReplyAddRequest';
export type { PostCommentReplyVO } from './models/PostCommentReplyVO';
export type { PostCommentVO } from './models/PostCommentVO';
export type { PostEditRequest } from './models/PostEditRequest';
export type { PostFavourAddRequest } from './models/PostFavourAddRequest';
export type { PostThumbAddRequest } from './models/PostThumbAddRequest';
export type { PostUpdateRequest } from './models/PostUpdateRequest';
export type { PostVO } from './models/PostVO';
export type { PrivateChatSessionAddRequest } from './models/PrivateChatSessionAddRequest';
export type { PrivateChatSessionVO } from './models/PrivateChatSessionVO';
export type { PrivateMessageAddRequest } from './models/PrivateMessageAddRequest';
export type { PrivateMessageVO } from './models/PrivateMessageVO';
export type { SseEmitter } from './models/SseEmitter';
export type { StopStreamingRequest } from './models/StopStreamingRequest';
export type { Teacher } from './models/Teacher';
export type { TeacherAddRequest } from './models/TeacherAddRequest';
export type { TeacherQueryRequest } from './models/TeacherQueryRequest';
export type { TeacherUpdateRequest } from './models/TeacherUpdateRequest';
export type { TeacherVO } from './models/TeacherVO';
export type { User } from './models/User';
export type { UserAddRequest } from './models/UserAddRequest';
export type { UserAiAvatarAddRequest } from './models/UserAiAvatarAddRequest';
export type { UserAiAvatarUpdateRequest } from './models/UserAiAvatarUpdateRequest';
export type { UserAiAvatarVO } from './models/UserAiAvatarVO';
export type { UserDailyWord } from './models/UserDailyWord';
export type { UserFeedback } from './models/UserFeedback';
export type { UserFeedbackAddRequest } from './models/UserFeedbackAddRequest';
export type { UserFeedbackProcessRequest } from './models/UserFeedbackProcessRequest';
export type { UserFeedbackReplyAddRequest } from './models/UserFeedbackReplyAddRequest';
export type { UserFeedbackReplyVO } from './models/UserFeedbackReplyVO';
export type { UserFeedbackUpdateRequest } from './models/UserFeedbackUpdateRequest';
export type { UserLearningRecordAddRequest } from './models/UserLearningRecordAddRequest';
export type { UserLearningRecordQueryRequest } from './models/UserLearningRecordQueryRequest';
export type { UserLearningRecordUpdateRequest } from './models/UserLearningRecordUpdateRequest';
export type { UserLearningRecordVO } from './models/UserLearningRecordVO';
export type { UserLearningStats } from './models/UserLearningStats';
export type { UserLevel } from './models/UserLevel';
export type { UserLevelAddRequest } from './models/UserLevelAddRequest';
export type { UserLevelQueryRequest } from './models/UserLevelQueryRequest';
export type { UserLevelUpdateRequest } from './models/UserLevelUpdateRequest';
export type { UserLevelVO } from './models/UserLevelVO';
export type { UserLoginByPhoneRequest } from './models/UserLoginByPhoneRequest';
export type { UserLoginRequest } from './models/UserLoginRequest';
export type { UserQueryRequest } from './models/UserQueryRequest';
export type { UserRegisterByPhoneRequest } from './models/UserRegisterByPhoneRequest';
export type { UserRegisterRequest } from './models/UserRegisterRequest';
export type { UserUpdateMyRequest } from './models/UserUpdateMyRequest';
export type { UserUpdateRequest } from './models/UserUpdateRequest';
export type { UserVO } from './models/UserVO';
export type { UserWordBookAddRequest } from './models/UserWordBookAddRequest';
export type { UserWordBookUpdateDifficultyRequest } from './models/UserWordBookUpdateDifficultyRequest';
export type { UserWordBookUpdateStatusRequest } from './models/UserWordBookUpdateStatusRequest';
export type { UserWordBookVO } from './models/UserWordBookVO';
export { AiAvatarChatControllerService } from './services/AiAvatarChatControllerService';
export { AiAvatarControllerService } from './services/AiAvatarControllerService';
export { AnnouncementControllerService } from './services/AnnouncementControllerService';
export { ChatControllerService } from './services/ChatControllerService';
export { CourseCategoryControllerService } from './services/CourseCategoryControllerService';
export { CourseChapterControllerService } from './services/CourseChapterControllerService';
export { CourseControllerService } from './services/CourseControllerService';
export { CourseFavouriteControllerService } from './services/CourseFavouriteControllerService';
export { CourseMaterialControllerService } from './services/CourseMaterialControllerService';
export { CourseReviewControllerService } from './services/CourseReviewControllerService';
export { CourseSectionControllerService } from './services/CourseSectionControllerService';
export { DailyArticleControllerService } from './services/DailyArticleControllerService';
export { DailyArticleFavourControllerService } from './services/DailyArticleFavourControllerService';
export { DailyArticleThumbControllerService } from './services/DailyArticleThumbControllerService';
export { DailyWordControllerService } from './services/DailyWordControllerService';
export { DailyWordLearningControllerService } from './services/DailyWordLearningControllerService';
export { DailyWordThumbControllerService } from './services/DailyWordThumbControllerService';
export { FileControllerService } from './services/FileControllerService';
export { FriendRelationshipControllerService } from './services/FriendRelationshipControllerService';
export { FriendRequestControllerService } from './services/FriendRequestControllerService';
export { PostCommentControllerService } from './services/PostCommentControllerService';
export { PostCommentReplyControllerService } from './services/PostCommentReplyControllerService';
export { PostControllerService } from './services/PostControllerService';
export { PostFavourControllerService } from './services/PostFavourControllerService';
export { PostThumbControllerService } from './services/PostThumbControllerService';
export { Service } from './services/Service';
export { TeacherControllerService } from './services/TeacherControllerService';
export { UserAiAvatarControllerService } from './services/UserAiAvatarControllerService';
export { UserControllerService } from './services/UserControllerService';
export { UserFeedbackControllerService } from './services/UserFeedbackControllerService';
export { UserFeedbackReplyControllerService } from './services/UserFeedbackReplyControllerService';
export { UserLearningRecordControllerService } from './services/UserLearningRecordControllerService';
export { UserLearningStatsControllerService } from './services/UserLearningStatsControllerService';
export { UserLevelControllerService } from './services/UserLevelControllerService';
export { UserWordBookControllerService } from './services/UserWordBookControllerService';
export { WxMpControllerService } from './services/WxMpControllerService';
