/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type UserLearningRecordQueryRequest = {
  current?: number;
  endDate?: string;
  id?: number;
  keyword?: string;
  lessonNumber?: number;
  maxAccuracy?: number;
  maxCount?: number;
  maxDuration?: number;
  maxExperience?: number;
  maxPoints?: number;
  minAccuracy?: number;
  minCount?: number;
  minDuration?: number;
  minExperience?: number;
  minPoints?: number;
  pageSize?: number;
  recordType?: string;
  relatedId?: number;
  sortField?: string;
  sortOrder?: string;
  startDate?: string;
  status?: string;
  userId?: number;
};
