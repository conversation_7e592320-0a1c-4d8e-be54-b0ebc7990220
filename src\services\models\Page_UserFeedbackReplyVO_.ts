/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { OrderItem } from './OrderItem';
import type { UserFeedbackReplyVO } from './UserFeedbackReplyVO';
export type Page_UserFeedbackReplyVO_ = {
  countId?: string;
  current?: number;
  maxLimit?: number;
  optimizeCountSql?: boolean;
  orders?: Array<OrderItem>;
  pages?: number;
  records?: Array<UserFeedbackReplyVO>;
  searchCount?: boolean;
  size?: number;
  total?: number;
};
