/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { OrderItem } from './OrderItem';
import type { PostCommentReplyVO } from './PostCommentReplyVO';
export type Page_PostCommentReplyVO_ = {
  countId?: string;
  current?: number;
  maxLimit?: number;
  optimizeCountSql?: boolean;
  orders?: Array<OrderItem>;
  pages?: number;
  records?: Array<PostCommentReplyVO>;
  searchCount?: boolean;
  size?: number;
  total?: number;
};
