<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    <group android:scaleX="0.54"
        android:scaleY="0.54"
        android:translateX="24.84"
        android:translateY="24.84">
        
        <!-- 将整体内容下移 -->
        <group android:translateY="20">
            <!-- 星星元素 -->
            <path
                android:pathData="M40,40 L42.5,48 L36,44 L44,44 L37.5,48 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M170,45 L173,53 L166.5,49 L179.5,49 L173,53 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M180,110 L182.5,118 L176,114 L184,114 L177.5,118 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M25,130 L27.5,138 L21,134 L29,134 L22.5,138 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M100,20 L103,28 L96.5,24 L109.5,24 L103,28 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M155,155 L157.5,163 L151,159 L159,159 L152.5,163 Z"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M60,60m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M160,130m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M40,150m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#a0d8ff" />
            <path
                android:pathData="M190,60m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#a0d8ff" />

            <!-- 云形状 -->
            <path
                android:pathData="M60,120 C40,120 30,105 30,90 C30,75 40,60 60,60 C60,40 80,25 100,25 C120,25 140,40 140,60 C160,60 170,75 170,90 C170,105 160,120 140,120 Z"
                android:fillColor="#f8fbff"
                android:strokeColor="#4a90e2"
                android:strokeWidth="2.5" />

            <!-- 数据流动效果 -->
            <path
                android:pathData="M40,90 L60,90 L70,80 L90,80 L100,90 L120,90 L130,80 L150,80"
                android:strokeWidth="1.5"
                android:strokeColor="#5a9ee2"
                android:strokeLineCap="butt"
                android:strokeLineJoin="miter"
                android:fillColor="#00000000" />
            <path
                android:pathData="M50,100 L70,100 L80,110 L100,110 L110,100 L130,100 L140,110 L160,110"
                android:strokeWidth="1.5"
                android:strokeColor="#5a9ee2"
                android:strokeLineCap="butt"
                android:strokeLineJoin="miter"
                android:fillColor="#00000000" />
            <path
                android:pathData="M90,40 C110,50 120,70 100,90"
                android:strokeWidth="1"
                android:strokeColor="#5a9ee2"
                android:strokeLineCap="butt"
                android:strokeLineJoin="miter"
                android:fillColor="#00000000" />
            <path
                android:pathData="M70,40 C50,60 60,80 80,90"
                android:strokeWidth="1"
                android:strokeColor="#5a9ee2"
                android:strokeLineCap="butt"
                android:strokeLineJoin="miter"
                android:fillColor="#00000000" />

            <!-- 中心科技图案 -->
            <group android:translateX="100" android:translateY="70">
                <path
                    android:pathData="M0,0m-18,0a18,18 0,1 1,36 0a18,18 0,1 1,-36 0"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:strokeLineCap="butt"
                    android:strokeLineJoin="miter"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M0,0m-12,0a12,12 0,1 1,24 0a12,12 0,1 1,-24 0"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M0,0m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0"
                    android:fillColor="#5a9ee2" />

                <!-- 连接线 -->
                <path
                    android:pathData="M-18,0L-25,0"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M18,0L25,0"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M0,-18L0,-25"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M0,18L0,25"
                    android:strokeWidth="1.5"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />

                <path
                    android:pathData="M-12,-12L-17,-17"
                    android:strokeWidth="1"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M12,-12L17,-17"
                    android:strokeWidth="1"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M-12,12L-17,17"
                    android:strokeWidth="1"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
                <path
                    android:pathData="M12,12L17,17"
                    android:strokeWidth="1"
                    android:strokeColor="#5a9ee2"
                    android:fillColor="#00000000" />
            </group>

            <!-- 智能元素 -->
            <path
                android:pathData="M85,65m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0"
                android:fillColor="#4a90e2" />
            <path
                android:pathData="M115,65m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0"
                android:fillColor="#4a90e2" />

            <!-- 额外的连接点 -->
            <path
                android:pathData="M70,60m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#4a90e2" />
            <path
                android:pathData="M130,60m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#4a90e2" />
            <path
                android:pathData="M100,105m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#4a90e2" />
            <path
                android:pathData="M65,95m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#4a90e2" />
            <path
                android:pathData="M135,95m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
                android:fillColor="#4a90e2" />
        </group>
    </group>
</vector> 