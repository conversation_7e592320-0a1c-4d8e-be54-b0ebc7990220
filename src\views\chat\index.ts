import AIChatDetail from './AIChatDetail.vue';
import Chat<PERSON>ontainer from './ChatContainer.vue';
import ChatHistory from './ChatHistory.vue';
import { AddFriend, FriendRequests } from './friends';
import IntelligenceCenter from './IntelligenceCenter.vue';
import UserChatDetail from './UserChatDetail.vue';

export {
  ChatHistory,
  AIChatDetail,
  IntelligenceCenter,
  ChatContainer,
  UserChatDetail,
  FriendRequests,
  AddFriend,
};

export default {
  ChatHistory,
  AIChatDetail,
  IntelligenceCenter,
  ChatContainer,
  UserChatDetail,
  FriendRequests,
  AddFriend,
};
