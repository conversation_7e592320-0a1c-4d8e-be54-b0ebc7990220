/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CourseReviewVO } from './CourseReviewVO';
import type { OrderItem } from './OrderItem';
export type Page_CourseReviewVO_ = {
  countId?: string;
  current?: number;
  maxLimit?: number;
  optimizeCountSql?: boolean;
  orders?: Array<OrderItem>;
  pages?: number;
  records?: Array<CourseReviewVO>;
  searchCount?: boolean;
  size?: number;
  total?: number;
};
