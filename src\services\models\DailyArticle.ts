/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type DailyArticle = {
  adminId?: number;
  author?: string;
  category?: string;
  content?: string;
  coverImage?: string;
  createTime?: string;
  difficulty?: number;
  id?: number;
  isDelete?: number;
  likeCount?: number;
  publishDate?: string;
  readTime?: number;
  source?: string;
  sourceUrl?: string;
  summary?: string;
  tags?: string;
  title?: string;
  updateTime?: string;
  viewCount?: number;
};
