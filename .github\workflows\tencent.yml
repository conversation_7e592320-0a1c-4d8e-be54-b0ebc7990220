# This workflow will build a docker container, publish and deploy it to Tencent Kubernetes Engine (TKE) when there is a push to the "main" branch.
#
# To configure this workflow:
#
# 1. Ensure that your repository contains the necessary configuration for your Tencent Kubernetes Engine cluster,
#    including deployment.yml, kustomization.yml, service.yml, etc.
#
# 2. Set up secrets in your workspace:
#    - TENCENT_CLOUD_SECRET_ID with Tencent Cloud secret id
#    - TENCENT_CLOUD_SECRET_KEY with Tencent Cloud secret key
#    - TENCENT_CLOUD_ACCOUNT_ID with Tencent Cloud account id
#    - TKE_REGISTRY_PASSWORD with TKE registry password
#
# 3. Change the values for the TKE_IMAGE_URL, TKE_REGION, TKE_CLUSTER_ID and DEPLOYMENT_NAME environment variables (below).

name: Tencent Kubernetes Engine

on:
  push:
    branches: ['main']

# Environment variables available to all jobs and steps in this workflow
env:
  TKE_IMAGE_URL: ccr.ccs.tencentyun.com/demo/mywebapp
  TKE_REGION: ap-guangzhou
  TKE_CLUSTER_ID: cls-mywebapp
  DEPLOYMENT_NAME: tke-test

permissions:
  contents: read

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Build
      - name: Build Docker image
        run: |
          docker build -t ${TKE_IMAGE_URL}:${GITHUB_SHA} .

      - name: Login TKE Registry
        run: |
          docker login -u ${{ secrets.TENCENT_CLOUD_ACCOUNT_ID }} -p '${{ secrets.TKE_REGISTRY_PASSWORD }}' ${TKE_IMAGE_URL}

      # Push the Docker image to TKE Registry
      - name: Publish
        run: |
          docker push ${TKE_IMAGE_URL}:${GITHUB_SHA}

      - name: Set up Kustomize
        run: |
          curl -o kustomize --location https://github.com/kubernetes-sigs/kustomize/releases/download/v3.1.0/kustomize_3.1.0_linux_amd64
          chmod u+x ./kustomize

      - name: Set up ~/.kube/config for connecting TKE cluster
        uses: TencentCloud/tke-cluster-credential-action@v1
        with:
          secret_id: ${{ secrets.TENCENT_CLOUD_SECRET_ID }}
          secret_key: ${{ secrets.TENCENT_CLOUD_SECRET_KEY }}
          tke_region: ${{ env.TKE_REGION }}
          cluster_id: ${{ env.TKE_CLUSTER_ID }}

      - name: Switch to TKE context
        run: |
          kubectl config use-context ${TKE_CLUSTER_ID}-context-default

      # Deploy the Docker image to the TKE cluster
      - name: Deploy
        run: |
          ./kustomize edit set image ${TKE_IMAGE_URL}:${GITHUB_SHA}
          ./kustomize build . | kubectl apply -f -
          kubectl rollout status deployment/${DEPLOYMENT_NAME}
          kubectl get services -o wide
