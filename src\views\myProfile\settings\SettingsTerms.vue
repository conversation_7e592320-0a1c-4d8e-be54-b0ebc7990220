<template>
  <div class="terms-container">
    <back-button title="用户协议" />

    <div class="terms-content">
      <div class="content-section">
        <h2 class="section-title">智云星课用户协议</h2>
        <div class="section-content">
          <p>
            本协议是您与智云星课平台（以下简称"我们"或"平台"）之间就平台服务等相关事宜所订立的契约。请您仔细阅读本协议，确保充分理解各条款内容。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">1. 协议的接受</h2>
        <div class="section-content">
          <p>
            欢迎使用智云星课！本协议适用于您使用我们提供的所有服务和功能。通过注册账号、登录、使用或以任何方式访问我们的服务，即表示您已同意受本协议全部条款的约束。如您不同意本协议任何条款，请勿注册或使用我们的服务。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">2. 账号注册与安全</h2>
        <div class="section-content">
          <p>
            在注册过程中，您需要提供真实、准确、完整的个人资料，并保证这些资料的真实性和及时更新。您应当妥善保管账号及密码信息，对您账号下的所有行为负全部责任。如发现任何未经授权使用您账号的情况，请立即通知我们。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">3. 服务内容与使用规范</h2>
        <div class="section-content">
          <p>
            我们提供在线教育学习平台服务，包括但不限于课程学习、智能辅导、词汇练习等功能。您同意：
          </p>
          <ul>
            <li>仅将我们的服务用于合法目的，遵守所有适用的法律法规；</li>
            <li>不从事任何可能损害平台安全、正常运营的行为；</li>
            <li>不发布或传播任何违法、侵权、骚扰、诽谤等不良信息；</li>
            <li>不侵犯他人的知识产权或其他合法权益；</li>
            <li>不使用自动化程序、脚本等方式干扰平台服务。</li>
          </ul>
          <p>
            我们有权在您违反上述规定时，视情况采取警告、限制或终止服务等措施。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">4. 知识产权</h2>
        <div class="section-content">
          <p>
            智云星课平台及其关联方拥有平台上所有内容的知识产权，包括但不限于文字、图片、音频、视频、软件、程序、课程资料等。未经我们明确书面许可，您不得以任何形式复制、分发、修改、展示、表演、转载、链接、下载或传播平台内容。
          </p>
          <p>
            对于您在平台上创建、上传或分享的内容，您保留相应的知识产权，同时授予我们全球范围内免费、非独占、可转授权的许可，允许我们存储、使用、复制、修改、改编、发布、展示相关内容，以实现平台的服务功能。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">5. 隐私保护</h2>
        <div class="section-content">
          <p>
            我们高度重视用户隐私保护，承诺按照《智云星课隐私政策》收集、使用、存储和保护您的个人信息。请您务必仔细阅读并理解隐私政策的完整内容。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">6. 服务变更与终止</h2>
        <div class="section-content">
          <p>
            我们保留随时修改、中断或终止服务的权利，且无需对您或任何第三方负责。我们会尽量通过适当方式提前通知服务的重大变更。
          </p>
          <p>
            如您违反本协议或相关法律法规，我们有权暂停或终止向您提供全部或部分服务，且无需承担任何责任。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">7. 免责声明</h2>
        <div class="section-content">
          <p>
            我们努力提供安全、可靠的服务，但不保证服务完全无误、安全或不中断。对于因以下原因导致的损失，我们不承担责任：
          </p>
          <ul>
            <li>不可抗力或非我们可控制的原因；</li>
            <li>您的使用不当或未按照说明操作；</li>
            <li>第三方引起的问题（如网络服务提供商）；</li>
            <li>平台的计划维护或紧急维护。</li>
          </ul>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">8. 协议的修改</h2>
        <div class="section-content">
          <p>
            我们可能会不时修改本协议，修改后的协议一经发布即生效。对于重大变更，我们会通过适当方式通知您。如您继续使用我们的服务，即表示您接受修改后的协议。如您不同意修改内容，应立即停止使用我们的服务。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">9. 管辖与法律适用</h2>
        <div class="section-content">
          <p>
            本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。如双方就本协议内容或执行发生争议，应尽量友好协商解决；协商不成的，任何一方均有权将争议提交至有管辖权的人民法院诉讼解决。
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">10. 联系我们</h2>
        <div class="section-content">
          <p>如果您对本协议有任何疑问、建议或投诉，请联系我们：</p>
          <ul>
            <li>
              <strong>邮箱:</strong>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </li>
            <li>
              <strong>服务时间:</strong> 周一至周五 9:00-18:00（法定节假日除外）
            </li>
          </ul>
          <p>我们会在15个工作日内回复您的咨询。</p>
        </div>
      </div>

      <div class="content-section">
        <p class="agreement-date">本协议最后更新于：{{ currentDate }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BackButton } from '../../../components/Common';

// 获取当前日期并格式化为"yyyy年MM月dd日"格式
const currentYear = new Date().getFullYear();
const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
const currentDay = String(new Date().getDate()).padStart(2, '0');
const currentDate = ref(`${currentYear}年${currentMonth}月${currentDay}日`);
</script>

<style scoped>
.terms-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 20px;
}

.terms-content {
  margin: 12px 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.content-section {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.content-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin: 0 0 12px 0;
}

.section-content {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

p {
  margin: 0 0 8px 0;
}

p:last-child {
  margin-bottom: 0;
}

ul {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0 0 8px 0;
}

ul li {
  margin-bottom: 8px;
}

ul li:last-child {
  margin-bottom: 0;
}

strong {
  font-weight: 600;
}

a {
  color: #1989fa;
  text-decoration: none;
}

.agreement-date {
  font-size: 13px;
  color: #969799;
  text-align: center;
  margin-top: 10px;
}
</style>
