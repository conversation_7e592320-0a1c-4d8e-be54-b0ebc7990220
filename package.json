{"name": "letterlearn", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build --mode production", "build:dev": "rsbuild build --mode development", "check": "biome check --write", "dev": "rsbuild dev --open --mode development", "format": "prettier --write .", "lint": "eslint .", "preview": "rsbuild preview", "cap:sync": "npx cap sync", "cap:android": "npx cap sync android", "cap:open:android": "npx cap open android"}, "dependencies": {"@biomejs/cli-win32-x64": "^2.1.1", "@capacitor/android": "^6.0.0", "@capacitor/camera": "^6.0.0", "@capacitor/cli": "^6.2.1", "@capacitor/core": "^6.0.0", "@capacitor/network": "^6.0.0", "@capacitor/preferences": "^6.0.0", "@capacitor/splash-screen": "^6.0.0", "@capacitor/toast": "^6.0.0", "@microsoft/fetch-event-source": "^2.0.1", "@rspack/binding-win32-x64-msvc": "^1.4.6", "@types/dompurify": "^3.0.5", "@types/marked": "^5.0.2", "@vant/area-data": "^2.0.0", "axios": "^1.8.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "express-http-proxy": "^2.1.1", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "marked": "^15.0.11", "node-fetch": "^3.3.2", "okhttp": "^1.1.0", "pinia": "^3.0.1", "serve": "^14.2.4", "shiki": "^3.3.0", "vant": "^4.9.17", "vue": "^3.5.13", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.5.0", "vue3-cropper": "^0.4.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@eslint/js": "^9.19.0", "@rsbuild/core": "^1.2.8", "@rsbuild/plugin-vue": "^1.0.5", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "eslint": "^9.19.0", "globals": "^15.14.0", "install": "^0.13.0", "katex": "^0.16.22", "npm": "^11.3.0", "openapi-typescript-codegen": "^0.29.0", "prettier": "^3.4.2", "typescript-eslint": "^8.22.0"}}