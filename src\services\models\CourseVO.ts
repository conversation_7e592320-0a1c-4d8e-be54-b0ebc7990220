/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { UserVO } from './UserVO';
export type CourseVO = {
  buyCount?: number;
  categoryId?: number;
  courseType?: number;
  coverImage?: string;
  createTime?: string;
  description?: string;
  difficulty?: number;
  id?: number;
  objectives?: string;
  originalPrice?: number;
  price?: number;
  ratingCount?: number;
  ratingScore?: number;
  requirements?: string;
  status?: number;
  studyCount?: number;
  subtitle?: string;
  tags?: string;
  targetAudience?: string;
  teacherAvatar?: string;
  teacherId?: number;
  teacherName?: string;
  teacherVO?: UserVO;
  title?: string;
  totalChapters?: number;
  totalDuration?: number;
  totalSections?: number;
  updateTime?: string;
};
