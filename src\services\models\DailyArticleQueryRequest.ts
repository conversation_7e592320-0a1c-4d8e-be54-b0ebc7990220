/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type DailyArticleQueryRequest = {
  adminId?: number;
  author?: string;
  category?: string;
  content?: string;
  createTime?: string;
  current?: number;
  difficulty?: number;
  id?: number;
  maxReadTime?: number;
  minReadTime?: number;
  minViewCount?: number;
  pageSize?: number;
  publishDateEnd?: string;
  publishDateStart?: string;
  sortField?: string;
  sortOrder?: string;
  source?: string;
  summary?: string;
  tags?: string;
  title?: string;
};
